import React, { useState, useContext } from 'react';
import { AuthContext } from '../../contexts/AuthContext';
import { AppLogo, EyeIcon, EyeSlashIcon } from '../icons/Icons';

const LoginPage: React.FC = () => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const authContext = useContext(AuthContext);
    
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        if (!username.trim() || !password.trim()) {
            setError('Username and password are required.');
            return;
        }
        setIsLoading(true);
        try {
            await authContext!.login(username, password);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="flex items-center justify-center min-h-screen bg-zinc-100 p-4">
            <div className="w-full max-w-md">
                <div className="bg-white border border-zinc-200/80 shadow-2xl shadow-zinc-200/50 rounded-2xl p-8 transform transition-all">
                    <div className="flex flex-col items-center mb-8">
                        <AppLogo className="w-16 h-16 text-violet-600 mb-4" />
                        <h1 className="text-4xl font-bold text-zinc-900">LifeTracker</h1>
                        <p className="text-zinc-500 mt-1">Sign in to continue</p>
                    </div>

                    <form onSubmit={handleSubmit} noValidate>
                        <div className="mb-4">
                            <label htmlFor="username" className="sr-only">Username</label>
                            <input
                                id="username"
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                placeholder="Username"
                                className="w-full px-4 py-3 bg-zinc-100 text-zinc-900 border border-zinc-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 transition-all"
                                required
                                autoComplete="username"
                            />
                        </div>

                        <div className="mb-6 relative">
                            <label htmlFor="password" className="sr-only">Password</label>
                            <input
                                id="password"
                                type={showPassword ? 'text' : 'password'}
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                placeholder="Password"
                                className="w-full px-4 py-3 bg-zinc-100 text-zinc-900 border border-zinc-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 transition-all"
                                required
                                autoComplete="current-password"
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute inset-y-0 right-0 flex items-center px-4 text-zinc-500 hover:text-violet-500 focus:outline-none focus:ring-2 focus:ring-violet-500 rounded-lg"
                                aria-label={showPassword ? "Hide password" : "Show password"}
                            >
                                {showPassword ? <EyeSlashIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                            </button>
                        </div>

                        {error && (
                            <div className="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg relative mb-4" role="alert">
                                <span className="block sm:inline">{error}</span>
                            </div>
                        )}

                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full bg-violet-600 text-white font-bold py-3 px-4 rounded-lg hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 focus:ring-offset-white disabled:bg-violet-400 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-300 transform hover:scale-105"
                        >
                            {isLoading ? (
                                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            ) : (
                                'Login'
                            )}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;