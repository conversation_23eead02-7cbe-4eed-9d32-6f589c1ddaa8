import React, { useState, useEffect } from 'react';
import { WeatherData } from '../../types';
import { fetchWeather, getWeatherDisplay } from '../../services/weatherService';

const WeatherWidgetSkeleton: React.FC = () => (
    <div className="bg-white rounded-xl p-5 animate-pulse border border-zinc-200">
        <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-zinc-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
                <div className="h-6 w-3/4 bg-zinc-200 rounded"></div>
                <div className="h-4 w-1/2 bg-zinc-200 rounded"></div>
            </div>
        </div>
        <div className="mt-6 flex justify-between space-x-2">
            {Array(4).fill(0).map((_, i) => (
                <div key={i} className="flex-1 flex flex-col items-center space-y-2 p-2 bg-zinc-100 rounded-lg">
                    <div className="h-4 w-8 bg-zinc-200 rounded"></div>
                    <div className="w-8 h-8 bg-zinc-200 rounded-full"></div>
                    <div className="h-4 w-12 bg-zinc-200 rounded"></div>
                </div>
            ))}
        </div>
    </div>
);

const WeatherWidget: React.FC = () => {
    const [weather, setWeather] = useState<WeatherData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const loadWeather = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await fetchWeather();
                setWeather(data);
            } catch (err) {
                if (err instanceof Error) {
                    setError(err.message);
                } else {
                    setError('An unknown error occurred.');
                }
            } finally {
                setLoading(false);
            }
        };

        loadWeather();
    }, []);

    if (loading) {
        return <WeatherWidgetSkeleton />;
    }

    if (error) {
        return (
            <div className="bg-red-100 border border-red-300 text-red-800 px-4 py-3 rounded-xl" role="alert">
                <strong className="font-bold">Weather Error: </strong>
                <span className="block sm:inline">{error}</span>
            </div>
        );
    }

    if (!weather) {
        return null;
    }

    const { current, forecast, locationName } = weather;
    const currentDisplay = getWeatherDisplay(current.weatherCode);

    return (
        <div className="bg-white rounded-2xl p-5 shadow-sm border border-zinc-200">
            <div className="flex items-start justify-between">
                <div>
                    <p className="text-zinc-500 text-sm">{locationName}</p>
                    <p className="text-4xl font-bold text-zinc-900 mt-1">{current.temperature}°F</p>
                    <p className="text-zinc-700">{currentDisplay.description}</p>
                    <p className="text-sm text-zinc-500">Feels like {current.feelsLike}°F</p>
                </div>
                <span className="text-5xl -mt-2">{currentDisplay.icon}</span>
            </div>
            <div className="mt-6 grid grid-cols-4 gap-3 text-center">
                {forecast.map((day, i) => {
                    const dayDisplay = getWeatherDisplay(day.weatherCode);
                    const date = new Date(day.date);
                    date.setMinutes(date.getMinutes() + date.getTimezoneOffset());
                    const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'short' });

                    return (
                        <div key={i} className="flex flex-col items-center space-y-1 p-2 bg-zinc-100 rounded-lg">
                            <p className="font-semibold text-sm text-zinc-600">{dayOfWeek}</p>
                            <span className="text-2xl">{dayDisplay.icon}</span>
                            <p className="text-sm font-medium text-zinc-800">{day.tempHigh}°</p>
                             <p className="text-sm font-medium text-zinc-500">{day.tempLow}°</p>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default WeatherWidget;
