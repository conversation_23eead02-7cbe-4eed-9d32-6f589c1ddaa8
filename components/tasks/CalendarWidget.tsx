import React, { useState, useContext, useMemo } from 'react';
import { TaskContext } from '../../contexts/TaskContext';
import { ChevronLeftIcon, ChevronRightIcon } from '../icons/Icons';

interface CalendarWidgetProps {
    onDateSelect: (date: Date | null) => void;
    selectedDate: Date | null;
}

const CalendarWidget: React.FC<CalendarWidgetProps> = ({ onDateSelect, selectedDate }) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const taskContext = useContext(TaskContext);

    if (!taskContext) throw new Error("TaskContext not found");

    const { tasks } = taskContext;

    const taskDates = useMemo(() => {
        const dates = new Set<string>();
        tasks.forEach(task => {
            if (!task.isCompleted) {
                dates.add(task.dueDate);
            }
        });
        return dates;
    }, [tasks]);

    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    
    const daysInMonth = [];
    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
        daysInMonth.push(new Date(currentDate.getFullYear(), currentDate.getMonth(), day));
    }

    const startingDayIndex = firstDayOfMonth.getDay(); // 0 for Sunday, 1 for Monday, ...
    
    const prevMonth = () => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
    const nextMonth = () => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));

    const isSameDay = (d1: Date, d2: Date | null) => {
        return d2 && d1.getFullYear() === d2.getFullYear() &&
               d1.getMonth() === d2.getMonth() &&
               d1.getDate() === d2.getDate();
    };

    const today = new Date();

    return (
        <div className="bg-white rounded-2xl shadow-sm p-4 border border-zinc-200">
            <div className="flex items-center justify-between mb-4">
                <button onClick={prevMonth} className="p-1.5 rounded-full text-zinc-500 hover:bg-zinc-100 hover:text-zinc-800" aria-label="Previous month">
                    <ChevronLeftIcon className="w-5 h-5" />
                </button>
                <h3 className="text-lg font-semibold text-zinc-900">
                    {currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}
                </h3>
                <button onClick={nextMonth} className="p-1.5 rounded-full text-zinc-500 hover:bg-zinc-100 hover:text-zinc-800" aria-label="Next month">
                    <ChevronRightIcon className="w-5 h-5" />
                </button>
            </div>
            <div className="grid grid-cols-7 gap-y-2 text-center text-sm">
                {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
                    <div key={i} className="font-medium text-zinc-400 w-10">{day}</div>
                ))}
                {Array(startingDayIndex).fill(null).map((_, i) => <div key={`empty-${i}`}></div>)}
                {daysInMonth.map(day => {
                    const dayString = day.toISOString().split('T')[0];
                    const hasTask = taskDates.has(dayString);
                    const isToday = isSameDay(day, today);
                    const isSelected = isSameDay(day, selectedDate);

                    const buttonClasses = `
                        relative w-9 h-9 flex items-center justify-center rounded-full transition-colors duration-200
                        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-violet-500
                        ${isSelected 
                            ? 'bg-violet-600 text-white font-bold shadow-lg shadow-violet-200' 
                            : 'text-zinc-700 hover:bg-zinc-100'}
                        ${isToday && !isSelected ? 'border border-violet-500/80' : ''}
                    `;

                    return (
                        <div key={day.toString()} className="flex justify-center">
                            <button
                                onClick={() => onDateSelect(day)}
                                className={buttonClasses}
                            >
                                <span>{day.getDate()}</span>
                                {hasTask && !isSelected && <div className="absolute bottom-1.5 w-1.5 h-1.5 bg-violet-500 rounded-full"></div>}
                            </button>
                        </div>
                    )
                })}
            </div>
        </div>
    );
};

export default CalendarWidget;