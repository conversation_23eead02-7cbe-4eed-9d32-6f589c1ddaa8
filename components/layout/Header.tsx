import React from 'react';
import { View } from '../../types';
import { PlusIcon } from '../icons/Icons';
import TaskFormModal from '../tasks/TaskFormModal';

interface HeaderProps {
    currentView: View;
}

const viewTitles: Record<View, string> = {
    tasks: 'Tasks',
    settings: 'Settings',
    profile: 'User Profile'
};

const Header: React.FC<HeaderProps> = ({ currentView }) => {
    const [isModalOpen, setIsModalOpen] = React.useState(false);
    
    return (
        <>
            <header className="flex-shrink-0 bg-white/80 backdrop-blur-sm border-b border-zinc-200 h-20 px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-full">
                    <h1 className="text-2xl font-bold text-zinc-900">{viewTitles[currentView]}</h1>
                    <button
                        onClick={() => setIsModalOpen(true)}
                        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-violet-600 hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 focus:ring-offset-white transition-transform transform hover:scale-105"
                    >
                        <PlusIcon className="w-5 h-5 mr-2 -ml-1" />
                        New Task
                    </button>
                </div>
            </header>
            <TaskFormModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
            />
        </>
    );
};

export default Header;