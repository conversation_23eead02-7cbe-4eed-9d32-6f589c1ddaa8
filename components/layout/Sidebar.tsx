import React, { useContext } from 'react';
import { AuthContext } from '../../contexts/AuthContext';
import { View } from '../../types';
import { AppLogo, ListBulletIcon, Cog6ToothIcon, UserCircleIcon, ArrowLeftStartOnRectangleIcon } from '../icons/Icons';

interface SidebarProps {
    currentView: View;
    setCurrentView: (view: View) => void;
}

const NavItem: React.FC<{
    icon: React.ReactNode;
    label: string;
    isActive: boolean;
    onClick: () => void;
}> = ({ icon, label, isActive, onClick }) => {
    const baseClasses = "flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-violet-500 w-full";
    const activeClasses = "bg-violet-100 text-violet-700 font-semibold shadow-inner";
    const inactiveClasses = "text-zinc-600 hover:bg-zinc-200 hover:text-zinc-900";

    return (
        <li>
            <button onClick={onClick} className={`${baseClasses} ${isActive ? activeClasses : inactiveClasses}`}>
                {icon}
                <span className="ml-3">{label}</span>
            </button>
        </li>
    );
};

const Sidebar: React.FC<SidebarProps> = ({ currentView, setCurrentView }) => {
    const authContext = useContext(AuthContext);

    const navItems: { view: View, label: string, icon: React.ReactNode }[] = [
        { view: 'tasks', label: 'Tasks', icon: <ListBulletIcon className="w-5 h-5" /> },
        { view: 'settings', label: 'Settings', icon: <Cog6ToothIcon className="w-5 h-5" /> },
        { view: 'profile', label: 'Profile', icon: <UserCircleIcon className="w-5 h-5" /> },
    ];
    
    return (
        <aside className="w-60 bg-zinc-100 border-r border-zinc-200 flex flex-col transition-colors duration-300">
            <div className="flex items-center justify-center h-20 px-4 border-b border-zinc-200">
                <AppLogo className="w-8 h-8 text-violet-600" />
                <span className="ml-3 text-xl font-bold text-zinc-900">LifeTracker</span>
            </div>
            <nav className="flex-1 px-4 py-6">
                <ul className="space-y-2">
                    {navItems.map(item => (
                        <NavItem
                            key={item.view}
                            label={item.label}
                            icon={item.icon}
                            isActive={currentView === item.view}
                            onClick={() => setCurrentView(item.view)}
                        />
                    ))}
                </ul>
            </nav>
            <div className="px-4 py-4 border-t border-zinc-200">
                 <button 
                    onClick={() => authContext?.logout()}
                    className="flex items-center w-full px-3 py-2.5 rounded-md text-sm font-medium text-zinc-600 hover:bg-zinc-200 hover:text-zinc-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-violet-500"
                >
                    <ArrowLeftStartOnRectangleIcon className="w-5 h-5" />
                    <span className="ml-3">Logout</span>
                </button>
            </div>
        </aside>
    );
};

export default Sidebar;